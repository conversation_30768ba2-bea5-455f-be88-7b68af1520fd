#!/usr/bin/env groovy

pipeline {
  agent none
  stages {

    stage('Build') {
      steps {
        script {
          def build_nodes = [:]
          def docker_images = [
            armhf: "px4io/px4-dev-armhf:2023-06-26",
            arm64: "px4io/px4-dev-aarch64:2022-08-12",
            base: "px4io/px4-dev-ros2-foxy:2022-08-12",
            nuttx: "px4io/px4-dev-nuttx-focal:2022-08-12",
          ]

          def armhf_builds = [
            target: ["beaglebone_blue_default", "emlid_navio2_default", "px4_raspberrypi_default", "scumaker_pilotpi_default"],
            image: docker_images.armhf,
            archive: false
          ]

          def arm64_builds = [
            target: ["scumaker_pilotpi_arm64"],
            image: docker_images.arm64,
            archive: false
          ]

          def base_builds = [
            target: ["px4_sitl_default"],
            image: docker_images.base,
            archive: false
          ]

          def nuttx_builds_archive = [
            target: [
                     "airmind_mindpx-v2_default",
                     "ark_can-flow_canbootloader",
                     "ark_can-flow_default",
                     "ark_can-gps_canbootloader",
                     "ark_can-gps_default",
                     "ark_can-rtk-gps_canbootloader",
                     "ark_can-rtk-gps_default",
                     "ark_cannode_canbootloader",
                     "ark_cannode_default",
                     "ark_fmu-v6x_bootloader",
                     "ark_fmu-v6x_default",
                     "ark_fpv_bootloader",
                     "ark_fpv_default",
                     "ark_pi6x_bootloader",
                     "ark_pi6x_default",
                     "atl_mantis-edu_default",
                     "av_x-v1_default",
                     "bitcraze_crazyflie21_default",
                     "bitcraze_crazyflie_default",
                     "cuav_can-gps-v1_canbootloader",
                     "cuav_can-gps-v1_default",
                     "cuav_nora_default",
                     "cuav_x7pro_default",
                     "cubepilot_cubeorange_default",
                     "cubepilot_cubeorangeplus_default",
                     "cubepilot_cubeyellow_default",
                     "diatone_mamba-f405-mk2_default",
                     "flywoo_gn-f405_default",
                     "freefly_can-rtk-gps_canbootloader",
                     "freefly_can-rtk-gps_default",
                     "holybro_can-gps-v1_canbootloader",
                     "holybro_can-gps-v1_default",
                     "holybro_durandal-v1_default",
                     "holybro_kakutef7_default",
                     "holybro_kakuteh7_default",
                     "holybro_kakuteh7mini_default",
                     "holybro_kakuteh7v2_default",
                     "holybro_pix32v5_default",
                     "matek_gnss-m9n-f4_canbootloader",
                     "matek_gnss-m9n-f4_default",
                     "matek_h743-mini_default",
                     "matek_h743-slim_default",
                     "matek_h743_default",
                     "modalai_fc-v1_default",
                     "modalai_fc-v2_default",
                     "mro_ctrl-zero-classic_default",
                     "mro_ctrl-zero-f7-oem_default",
                     "mro_ctrl-zero-f7_default",
                     "mro_ctrl-zero-h7-oem_default",
                     "mro_ctrl-zero-h7_default",
                     "mro_pixracerpro_default",
                     "mro_x21-777_default",
                     "mro_x21_default",
                     "nxp_fmuk66-e_default",
                     "nxp_fmuk66-e_socketcan",
                     "nxp_fmuk66-v3_default",
                     "nxp_fmuk66-v3_socketcan",
                     "nxp_mr-canhubk3_default",
                     "nxp_ucans32k146_canbootloader",
                     "nxp_ucans32k146_default",
                     "omnibus_f4sd_default",
                     "px4_fmu-v2_default",
                     "px4_fmu-v2_fixedwing",
                     "px4_fmu-v2_lto",
                     "px4_fmu-v2_multicopter",
                     "px4_fmu-v2_rover",
                     "px4_fmu-v3_default",
                     "px4_fmu-v4_default",
                     "px4_fmu-v4pro_default",
                     "px4_fmu-v5_cyphal",
                     "px4_fmu-v5_debug",
                     "px4_fmu-v5_default",
                     "px4_fmu-v5_lto",
                     "px4_fmu-v5_rover",
                     "px4_fmu-v5_stackcheck",
                     "px4_fmu-v5_uavcanv0periph",
                     "px4_fmu-v5x_default",
                     "px4_fmu-v5x_rover",
                     "px4_fmu-v6c_default",
                     "px4_fmu-v6c_rover",
                     "px4_fmu-v6u_default",
                     "px4_fmu-v6u_rover",
                     "px4_fmu-v6x_default",
                     "px4_fmu-v6x_rover",
                     "px4_fmu-v6xrt_bootloader",
                     "px4_fmu-v6xrt_default",
                     "px4_fmu-v6xrt_rover",
                     "px4_io-v2_default",
                     "raspberrypi_pico_default",
                     "siyi_n7_default",
                     "sky-drones_smartap-airlink_default",
                     "spracing_h7extreme_default",
                     "thepeach_k1_default",
                     "thepeach_r1_default",
                     "uvify_core_default",
            ],
            image: docker_images.nuttx,
            archive: true
          ]

          def docker_builds = [
            armhf_builds, base_builds, nuttx_builds_archive
          ]

          for (def build_type = 0; build_type < docker_builds.size(); build_type++) {
            for (def build_target = 0; build_target < docker_builds[build_type].target.size(); build_target++) {
              build_nodes.put(docker_builds[build_type].target[build_target],
                createBuildNode(docker_builds[build_type].archive, docker_builds[build_type].image, docker_builds[build_type].target[build_target])
                )
            }
          }

        parallel build_nodes

        } // script
      } // steps
    } // stage Build

    // TODO: actually upload artifacts to S3
    // stage('S3 Upload') {
    //   agent {
    //     docker { image 'px4io/px4-dev-base-focal:2021-09-08' }
    //   }
    //   options {
    //         skipDefaultCheckout()
    //   }
    //   when {
    //     anyOf {
    //       branch 'master'
    //       branch 'beta'
    //       branch 'stable'
    //       branch 'pr-jenkins' // for testing
    //     }
    //   }
    //   steps {
    //     sh 'echo "uploading to S3"'
    //   }
    // }

  } // stages
  environment {
    CCACHE_DIR = '/tmp/ccache'
    CI = true
  }
  options {
    buildDiscarder(logRotator(numToKeepStr: '5', artifactDaysToKeepStr: '14'))
    timeout(time: 120, unit: 'MINUTES')
  }
}

def createBuildNode(Boolean archive, String docker_image, String target) {
  return {

    bypass_entrypoint = ''

    node {
      docker.withRegistry('https://registry.hub.docker.com', 'docker_hub_dagar') {
        docker.image(docker_image).inside('-e CCACHE_BASEDIR=${WORKSPACE} -v ${CCACHE_DIR}:${CCACHE_DIR}:rw' + bypass_entrypoint) {
          stage(target) {
            try {
              sh('export')
              checkout(scm)
              sh('make distclean; git clean -ff -x -d .')
              sh('git fetch --tags')
              sh('ccache -s')
              sh('make ' + target)
              sh('ccache -s')
              sh('make sizes')
              if (archive) {
                archiveArtifacts(allowEmptyArchive: false, artifacts: 'build/*/*.px4, build/*/*.elf, build/*/*.bin', fingerprint: true, onlyIfSuccessful: true)
              }
              sh('make ' + target + ' package')
              archiveArtifacts(allowEmptyArchive: true, artifacts: 'build/*/*.tar.bz2', fingerprint: true, onlyIfSuccessful: true)
              archiveArtifacts(allowEmptyArchive: true, artifacts: 'build/*/*.deb', fingerprint: true, onlyIfSuccessful: true)
            }
            catch (exc) {
              throw (exc)
            }
            finally {
              sh('make distclean; git clean -ff -x -d .')
            }
          }
        }
      }
    }
  }
}
