name: 🚀 Feature Request
description: Suggest an idea for this project
labels: ["feature-request"]
body:
  - type: markdown
    attributes:
      value: |
        ## Please note that feature requests are not 'fire and forget'
        It is a lot more likely that the feature you would like to have will be implemented if you keep watching your feature request, and provide more details to developers looking into implementing your feature, and help them with testing.

  - type: textarea
    attributes:
      label: Describe problem solved by the proposed feature
      description: A clear and concise description of the problem, if any, this feature will solve. E.g. I'm always frustrated when ...
    validations:
      required: true

  - type: textarea
    attributes:
      label: Describe your preferred solution
      description: A clear and concise description of what you want to happen.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Describe possible alternatives
      description: A clear and concise description of any alternative solutions or features you've considered.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Additional context
      description: Add any other context or screenshots for the feature request here.
