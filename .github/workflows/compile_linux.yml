name: Compile Linux Targets

on:
  push:
    branches:
    - 'main'
    - 'stable'
    - 'beta'
    - 'release/*'
  pull_request:
    branches:
    - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-armhf:2023-06-26
    strategy:
      matrix:
        config: [
          beaglebone_blue_default,
          emlid_navio2_default,
          px4_raspberrypi_default,
          scumaker_pilotpi_default,
          ]
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{secrets.ACCESS_TOKEN}}
    - name: ownership workaround
      run: git config --system --add safe.directory '*'
    - name: Prepare ccache timestamp
      id: ccache_cache_timestamp
      shell: cmake -P {0}
      run: |
        string(TIMESTAMP current_date "%Y-%m-%d-%H;%M;%S" UTC)
        message("::set-output name=timestamp::${current_date}")
    - name: ccache cache files
      uses: actions/cache@v2
      with:
        path: ~/.ccache
        key: ${{matrix.config}}-ccache-${{steps.ccache_cache_timestamp.outputs.timestamp}}
        restore-keys: ${{matrix.config}}-ccache-
    - name: setup ccache
      run: |
          mkdir -p ~/.ccache
          echo "base_dir = ${GITHUB_WORKSPACE}" > ~/.ccache/ccache.conf
          echo "compression = true" >> ~/.ccache/ccache.conf
          echo "compression_level = 6" >> ~/.ccache/ccache.conf
          echo "max_size = 100M" >> ~/.ccache/ccache.conf
          echo "hash_dir = false" >> ~/.ccache/ccache.conf
          ccache -s
          ccache -z

    - name: make ${{matrix.config}}
      run: make ${{matrix.config}}
    - name: ccache post-run
      run: ccache -s
