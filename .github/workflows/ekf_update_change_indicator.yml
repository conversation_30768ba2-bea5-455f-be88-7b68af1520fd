name: EKF Update Change Indicator

on: push

jobs:
  unit_tests:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-base-focal:2021-09-08
    env:
      GIT_COMMITTER_EMAIL: <EMAIL>
      GIT_COMMITTER_NAME: PX4BuildBot
    steps:
    - uses: actions/checkout@v2.3.1
      with:
        fetch-depth: 0
    - name: main test updates change indication files
      run: make tests TESTFILTER=EKF
    - name: Check if there exists diff and save result in variable
      run: echo "CHANGE_INDICATED=$(git diff --exit-code --output=/dev/null || echo $?)" >> $GITHUB_ENV
      working-directory: src/modules/ekf2/test/change_indication
    - name: auto-commit any changes to change indication
      uses: stefanzweifel/git-auto-commit-action@v4
      with:
        commit_message: '[AUTO COMMIT] update change indication'
        commit_user_name: ${GIT_COMMITTER_NAME}
        commit_user_email: ${GIT_COMMITTER_EMAIL}
    - if: ${{env.CHANGE_INDICATED}}
      name: if there is a functional change, fail check
      run: exit 1
