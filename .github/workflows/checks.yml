name: Checks

on:
  push:
    branches:
    - 'main'
  pull_request:
    branches:
    - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        check: [
          "check_format",
          "tests",
          "tests_coverage",
          "px4_fmu-v2_default stack_check",
          "validate_module_configs",
          "shellcheck_all",
          "NO_NINJA_BUILD=1 px4_fmu-v5_default",
          "NO_NINJA_BUILD=1 px4_sitl_default",
          "airframe_metadata",
          "module_documentation",
          "parameters_metadata",
        ]
    container:
      image: px4io/px4-dev-nuttx-focal:2022-08-12
      options: --privileged --ulimit core=-1 --security-opt seccomp=unconfined
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}

    - name: check environment
      run: |
          export
          ulimit -a
    - name: ${{matrix.check}}
      run: make ${{matrix.check}}
    - name: upload coverage
      if: contains(matrix.check, 'coverage')
      uses: codecov/codecov-action@v1
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        flags: unittests
        file: coverage/lcov.info
