name: Nuttx Target with extra env config

on:
  push:
    branches:
    - 'main'
  pull_request:
    branches:
    - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-nuttx-focal:2022-08-12
    strategy:
      matrix:
        config: [
          px4_fmu-v5,
          ]
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{secrets.ACCESS_TOKEN}}

    - name: make ${{matrix.config}}
      env:
        PX4_EXTRA_NUTTX_CONFIG: "CONFIG_NSH_LOGIN_PASSWORD=\"test\";CONFIG_NSH_CONSOLE_LOGIN=y"
      run: |
        echo "PX4_EXTRA_NUTTX_CONFIG: $PX4_EXTRA_NUTTX_CONFIG"
        make ${{matrix.config}} nuttx_context
        # Check that the config option is set
        grep CONFIG_NSH_LOGIN_PASSWORD build/${{matrix.config}}_default/NuttX/nuttx/.config
