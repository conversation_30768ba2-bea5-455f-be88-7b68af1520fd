name: Metadata

on:
  push:
    branches:
    - 'main'
    - 'release/*'
    - 'pr-metadata-test'

jobs:

  airframe:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-base-focal:2021-09-08
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}

    - name: airframe metadata
      run: |
        make airframe_metadata
        ./src/lib/version/get_git_tag_or_branch_version.sh build/px4_sitl_default >> $GITHUB_ENV
        cd build/px4_sitl_default/docs
      # TODO: deploy to userguide gitbook

    - uses: jakejarvis/s3-sync-action@master
      with:
        args: --acl public-read
      env:
        AWS_S3_BUCKET: 'px4-travis'
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_REGION: 'us-west-1'
        SOURCE_DIR: 'build/px4_sitl_default/docs/'
        DEST_DIR: 'Firmware/${{ env.version }}/_general/'

  module:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-base-focal:2021-09-08
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}

    - name: module documentation
      run: |
        make module_documentation
        cd build/px4_sitl_default/docs
        ls -ls *
      # TODO: deploy to userguide gitbook and s3

  parameter:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-base-focal:2021-09-08
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}

    - name: parameter metadata
      run: |
        make parameters_metadata
        ./src/lib/version/get_git_tag_or_branch_version.sh build/px4_sitl_default >> $GITHUB_ENV

    - uses: jakejarvis/s3-sync-action@master
      with:
        args: --acl public-read
      env:
        AWS_S3_BUCKET: 'px4-travis'
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_REGION: 'us-west-1'
        SOURCE_DIR: 'build/px4_sitl_default/docs/'
        DEST_DIR: 'Firmware/${{ env.version }}/_general/'

  events:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-base-focal:2021-09-08
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}

    - name: events metadata
      run: |
        make extract_events
        ./src/lib/version/get_git_tag_or_branch_version.sh build/px4_sitl_default >> $GITHUB_ENV
        cd build/px4_sitl_default
        mkdir _events_full || true
        cp events/all_events_full.json.xz _events_full/all_events.json.xz

    - uses: jakejarvis/s3-sync-action@master
      with:
        args: --acl public-read
      env:
        AWS_S3_BUCKET: 'px4-travis'
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_REGION: 'us-west-1'
        SOURCE_DIR: 'build/px4_sitl_default/_events_full/'
        DEST_DIR: 'Firmware/${{ env.version }}/_general/'

  uorb_graph:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-nuttx-focal:2022-08-12
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}

    - name: uORB graph
      run: |
        make uorb_graphs
        cd Tools/uorb_graph
        ls -ls *
      # TODO: deploy graph_px4_sitl.json to S3 px4-travis:Firmware/master/

  ROS2_msgs:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-base-focal:2021-09-08
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}

    - name: PX4 ROS2 msgs
      run: |
        git clone https://github.com/PX4/px4_msgs.git
        rm px4_msgs/msg/*.msg
        rm px4_msgs/srv/*.srv
        cp msg/*.msg px4_msgs/msg/
        cp srv/*.srv px4_msgs/srv/
