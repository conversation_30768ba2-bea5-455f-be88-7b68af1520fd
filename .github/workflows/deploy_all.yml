name: Deploy metadata for all targets

on:
  push:
    branches:
    - 'main'
    - 'release/*'
    - 'pr-metadata-test'

jobs:
  enumerate_targets:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-base-focal:2021-09-08
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{secrets.ACCESS_TOKEN}}
    - id: set-matrix
      run: echo "::set-output name=matrix::$(./Tools/generate_board_targets_json.py)"
  build:
    runs-on: ubuntu-latest
    needs: enumerate_targets
    strategy:
      matrix: ${{fromJson(needs.enumerate_targets.outputs.matrix)}}
    container: ${{ matrix.container }}
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{secrets.ACCESS_TOKEN}}

    - name: ownership workaround
      run: git config --system --add safe.directory '*'

    - name: make ${{matrix.target}}
      run: make ${{matrix.target}}

    - name: parameter & events metadata
      run: |
        make ${{matrix.target}} ver_gen events_json actuators_json
        ./src/lib/version/get_git_tag_or_branch_version.sh build/${{ matrix.target }} >> $GITHUB_ENV
        cd build/${{ matrix.target }}
        mkdir _metadata || true
        cp parameters.* events/*.xz actuators.json* _metadata

    - uses: jakejarvis/s3-sync-action@master
      with:
        args: --acl public-read
      env:
        AWS_S3_BUCKET: 'px4-travis'
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_REGION: 'us-west-1'
        SOURCE_DIR: 'build/${{ matrix.target }}/_metadata/'
        DEST_DIR: 'Firmware/${{ env.version }}/${{ matrix.target }}/'

