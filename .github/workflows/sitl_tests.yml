name: SITL Tests

on:
  push:
    branches:
    - 'main'
  pull_request:
    branches:
    - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        config:
          - {model: "iris",          latitude:  "59.617693", longitude: "-151.145316", altitude:  "48", build_type: "RelWithDebInfo" } # Alaska
          # - {model: "standard_vtol", latitude: "-38.071235", longitude:  "145.281220", altitude:  "31", build_type: "AddressSanitizer" } # Australia
          - {model: "tailsitter" ,   latitude:  "29.660316", longitude:  "-82.316658", altitude:  "30", build_type: "RelWithDebInfo" } # Florida
          - {model: "standard_vtol", latitude:  "47.397742", longitude:    "8.545594", altitude: "488", build_type: "Coverage" } # Zurich

    container:
      image: px4io/px4-dev-simulation-focal:2021-09-08
      options: --privileged --ulimit core=-1 --security-opt seccomp=unconfined
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{ secrets.ACCESS_TOKEN }}

    - name: Download MAVSDK
      run: wget "https://github.com/mavlink/MAVSDK/releases/download/v$(cat test/mavsdk_tests/MAVSDK_VERSION)/libmavsdk-dev_$(cat test/mavsdk_tests/MAVSDK_VERSION)_ubuntu20.04_amd64.deb"
    - name: Install MAVSDK
      run: dpkg -i "libmavsdk-dev_$(cat test/mavsdk_tests/MAVSDK_VERSION)_ubuntu20.04_amd64.deb"

    - name: Prepare ccache timestamp
      id: ccache_cache_timestamp
      shell: cmake -P {0}
      run: |
        string(TIMESTAMP current_date "%Y-%m-%d-%H;%M;%S" UTC)
        message("::set-output name=timestamp::${current_date}")
    - name: ccache cache files
      uses: actions/cache@v2
      with:
        path: ~/.ccache
        key: sitl_tests-${{matrix.config.build_type}}-ccache-${{steps.ccache_cache_timestamp.outputs.timestamp}}
        restore-keys: sitl_tests-${{matrix.config.build_type}}-ccache-
    - name: setup ccache
      run: |
          mkdir -p ~/.ccache
          echo "base_dir = ${GITHUB_WORKSPACE}" > ~/.ccache/ccache.conf
          echo "compression = true" >> ~/.ccache/ccache.conf
          echo "compression_level = 6" >> ~/.ccache/ccache.conf
          echo "max_size = 100M" >> ~/.ccache/ccache.conf
          echo "hash_dir = false" >> ~/.ccache/ccache.conf
          ccache -s
          ccache -z

    - name: check environment
      env:
        PX4_HOME_LAT: ${{matrix.config.latitude}}
        PX4_HOME_LON: ${{matrix.config.longitude}}
        PX4_HOME_ALT: ${{matrix.config.altitude}}
        PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
      run: |
          export
          ulimit -a
    - name: Build PX4
      env:
        PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
      run: make px4_sitl_default
    - name: ccache post-run px4/firmware
      run: ccache -s
    - name: Build SITL Gazebo
      env:
        PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
      run: make px4_sitl_default sitl_gazebo-classic
    - name: ccache post-run sitl_gazebo-classic
      run: ccache -s
    - name: Build MAVSDK tests
      env:
        PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
        DONT_RUN: 1
      run: make px4_sitl_default sitl_gazebo-classic mavsdk_tests
    - name: ccache post-run mavsdk_tests
      run: ccache -s

    - name: Core dump settings
      run: |
          ulimit -c unlimited
          echo "`pwd`/%e.core" > /proc/sys/kernel/core_pattern

    - name: Run SITL tests
      env:
        PX4_HOME_LAT: ${{matrix.config.latitude}}
        PX4_HOME_LON: ${{matrix.config.longitude}}
        PX4_HOME_ALT: ${{matrix.config.altitude}}
        PX4_CMAKE_BUILD_TYPE: ${{matrix.config.build_type}}
      run: test/mavsdk_tests/mavsdk_test_runner.py --speed-factor 20 --abort-early --model ${{matrix.config.model}} --upload test/mavsdk_tests/configs/sitl.json --verbose
      timeout-minutes: 45

    - name: Look at core files
      if: failure()
      run: gdb build/px4_sitl_default/bin/px4 px4.core -ex "thread apply all bt" -ex "quit"
    - name: Upload px4 coredump
      if: failure()
      uses: actions/upload-artifact@v2-preview
      with:
        name: coredump
        path: px4.core

    - name: Upload px4 binary
      if: failure()
      uses: actions/upload-artifact@v2-preview
      with:
        name: binary
        path: build/px4_sitl_default/bin/px4

    # Report test coverage
    - name: Upload coverage
      if: contains(matrix.config.build_type, 'Coverage')
      run: |
          git config --global credential.helper "" # disable the keychain credential helper
          git config --global --add credential.helper store # enable the local store credential helper
          echo "https://x-access-token:${{ secrets.ACCESS_TOKEN }}@github.com" >> ~/.git-credentials # add credential
          git config --global url."https://github.com/".insteadof **************: # credentials add credential
          mkdir -p coverage
          lcov --directory build/px4_sitl_default --base-directory build/px4_sitl_default --gcov-tool gcov --capture -o coverage/lcov.info
    - name: Upload coverage information to Codecov
      if: contains(matrix.config.build_type, 'Coverage')
      uses: codecov/codecov-action@v1
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        flags: mavsdk
        file: coverage/lcov.info
