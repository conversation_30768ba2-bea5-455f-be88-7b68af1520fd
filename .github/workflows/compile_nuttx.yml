name: Compile <PERSON>uttx Targets

on:
  push:
    branches:
    - 'main'
    - 'stable'
    - 'beta'
    - 'release/*'
  pull_request:
    branches:
    - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-nuttx-focal:2022-08-12
    strategy:
      fail-fast: false
      matrix:
        config: [
          airmind_mindpx-v2,
          ark_can-flow,
          ark_can-gps,
          ark_can-rtk-gps,
          ark_cannode,
          ark_fmu-v6x,
          ark_pi6x,
          ark_septentrio-gps,
          atl_mantis-edu,
          av_x-v1,
          bitcraze_crazyflie,
          bitcraze_crazyflie21,
          cuav_can-gps-v1,
          cuav_nora,
          cuav_x7pro,
          cubepilot_cubeorange,
          cubepilot_cubeorangeplus,
          cubepilot_cubeyellow,
          diatone_mamba-f405-mk2,
          freefly_can-rtk-gps,
          holybro_can-gps-v1,
          holybro_durandal-v1,
          holybro_kakutef7,
          holy<PERSON>_kakuteh7,
          holybro_pix32v5,
          matek_gnss-m9n-f4,
          matek_h743,
          matek_h743-mini,
          matek_h743-slim,
          modalai_fc-v1,
          modalai_fc-v2,
          mro_ctrl-zero-classic,
          mro_ctrl-zero-f7,
          mro_ctrl-zero-f7-oem,
          mro_ctrl-zero-h7,
          mro_ctrl-zero-h7-oem,
          mro_pixracerpro,
          mro_x21,
          mro_x21-777,
          nxp_fmuk66-e,
          nxp_fmuk66-v3,
          nxp_mr-canhubk3,
          nxp_ucans32k146,
          omnibus_f4sd,
          px4_fmu-v2,
          px4_fmu-v3,
          px4_fmu-v4,
          px4_fmu-v4pro,
          px4_fmu-v5,
          px4_fmu-v5x,
          px4_fmu-v6c,
          px4_fmu-v6u,
          px4_fmu-v6x,
          px4_fmu-v6xrt,
          raspberrypi_pico,
          sky-drones_smartap-airlink,
          spracing_h7extreme,
          uvify_core,
          siyi_n7
          ]
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{secrets.ACCESS_TOKEN}}

    - name: Prepare ccache timestamp
      id: ccache_cache_timestamp
      shell: cmake -P {0}
      run: |
        string(TIMESTAMP current_date "%Y-%m-%d-%H;%M;%S" UTC)
        message("::set-output name=timestamp::${current_date}")
    - name: ccache cache files
      uses: actions/cache@v2
      with:
        path: ~/.ccache
        key: ${{matrix.config}}-ccache-${{steps.ccache_cache_timestamp.outputs.timestamp}}
        restore-keys: ${{matrix.config}}-ccache-
    - name: setup ccache
      run: |
          mkdir -p ~/.ccache
          echo "base_dir = ${GITHUB_WORKSPACE}" > ~/.ccache/ccache.conf
          echo "compression = true" >> ~/.ccache/ccache.conf
          echo "compression_level = 6" >> ~/.ccache/ccache.conf
          echo "max_size = 120M" >> ~/.ccache/ccache.conf
          echo "hash_dir = false" >> ~/.ccache/ccache.conf
          ccache -s
          ccache -z

    - name: make all_variants_${{matrix.config}}
      run: make all_variants_${{matrix.config}}
      timeout-minutes: 45
    - name: make ${{matrix.config}} bloaty_compileunits
      run: make ${{matrix.config}} bloaty_compileunits || true
    - name: make ${{matrix.config}} bloaty_inlines
      run: make ${{matrix.config}} bloaty_inlines || true
    - name: make ${{matrix.config}} bloaty_segments
      run: make ${{matrix.config}} bloaty_segments || true
    - name: make ${{matrix.config}} bloaty_symbols
      run: make ${{matrix.config}} bloaty_symbols || true
    - name: make ${{matrix.config}} bloaty_templates
      run: make ${{matrix.config}} bloaty_templates || true
    - name: make ${{matrix.config}} bloaty_ram
      run: make ${{matrix.config}} bloaty_ram || true
    - name: make ${{matrix.config}} bloaty_compare_master
      run: make ${{matrix.config}} bloaty_compare_master || true
    - name: ccache post-run
      run: ccache -s

    - name: Upload px4 package
      uses: actions/upload-artifact@v2
      with:
        name: px4_package_${{matrix.config}}
        path: |
          build/**/*.px4
          build/**/*.bin
