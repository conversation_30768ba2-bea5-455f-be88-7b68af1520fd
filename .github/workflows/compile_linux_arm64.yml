name: Compile Linux ARM64 Targets

on:
  push:
    branches:
    - 'main'
    - 'stable'
    - 'beta'
    - 'release/*'
  pull_request:
    branches:
    - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    container: px4io/px4-dev-aarch64:2022-08-12
    strategy:
      matrix:
        config: [
          scumaker_pilotpi_arm64,
          ]
    steps:
    - uses: actions/checkout@v1
      with:
        token: ${{secrets.ACCESS_TOKEN}}

    - name: Prepare ccache timestamp
      id: ccache_cache_timestamp
      shell: cmake -P {0}
      run: |
        string(TIMESTAMP current_date "%Y-%m-%d-%H;%M;%S" UTC)
        message("::set-output name=timestamp::${current_date}")
    - name: ccache cache files
      uses: actions/cache@v2
      with:
        path: ~/.ccache
        key: ${{matrix.config}}-ccache-${{steps.ccache_cache_timestamp.outputs.timestamp}}
        restore-keys: ${{matrix.config}}-ccache-
    - name: setup ccache
      run: |
          mkdir -p ~/.ccache
          echo "base_dir = ${GITHUB_WORKSPACE}" > ~/.ccache/ccache.conf
          echo "compression = true" >> ~/.ccache/ccache.conf
          echo "compression_level = 6" >> ~/.ccache/ccache.conf
          echo "max_size = 100M" >> ~/.ccache/ccache.conf
          echo "hash_dir = false" >> ~/.ccache/ccache.conf
          ccache -s
          ccache -z

    - name: make ${{matrix.config}}
      run: make ${{matrix.config}}
    - name: ccache post-run
      run: ccache -s
