#!/bin/sh
# Standard startup script for logging.
#
# NOTE: Script variables are declared/initialized/unset in the rcS script.
#

###############################################################################
#                 End Setup for board specific configurations.                #
###############################################################################

if param compare SDLOG_MODE 1
then
	set LOGGER_ARGS "${LOGGER_ARGS} -e"
fi

if param compare SDLOG_MODE 2
then
	set LOGGER_ARGS "${LOGGER_ARGS} -f"
fi

if param compare SDLOG_MODE 3
then
	set LOGGER_ARGS "${LOGGER_ARGS} -x"
fi

if param compare SDLOG_MODE 4
then
	set LOGGER_ARGS "${LOGGER_ARGS} -a"
fi


if ! param compare SDLOG_MODE -1
then
	logger start -b ${LOGGER_BUF} -t ${LOGGER_ARGS}
fi
