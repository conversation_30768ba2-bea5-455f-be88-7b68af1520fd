#!/bin/sh
#
# @name Cloudship
# @type Airship
# @class Airship
#
# @output Motor1 starboard thruster
# @output Motor2 port thruster
# @output Motor3 tail thruster
# @output Servo1 thrust tilt
#
# @board px4_fmu-v2 exclude
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.airship_defaults

param set-default COM_PREARM_MODE 2
param set-default CBRK_IO_SAFETY 22027

param set-default CA_AIRFRAME 9

param set-default CA_ROTOR_COUNT 3
param set-default CA_ROTOR0_AX 1
param set-default CA_ROTOR0_AZ 0
param set-default CA_ROTOR0_KM 0
param set-default CA_ROTOR0_PY 2
param set-default CA_ROTOR1_AX 1
param set-default CA_ROTOR1_AZ 0
param set-default CA_ROTOR1_KM 0
param set-default CA_ROTOR1_PY -2
param set-default CA_ROTOR2_AY -1
param set-default CA_ROTOR2_AZ 0
param set-default CA_ROTOR2_KM 0
param set-default CA_ROTOR2_PX -10

param set-default CA_SV_CS_COUNT 1
param set-default CA_SV_CS0_TRQ_P 1

param set-default CA_R_REV 7

