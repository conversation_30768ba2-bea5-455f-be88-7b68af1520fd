#!/bin/sh
#
# @name ThunderFly Auto-G2
#
# @type Autogyro
# @class Autogyro
#
# @output Motor1 throttle
# @output Servo1 rotor_head_L
# @output Servo2 rotor_head_R
# @output Servo3 elevator
# @output Servo4 rudder
# @output Servo5 rudder (second, optional)
# @output Servo6 wheel
#
# @url https://github.com/ThunderFly-aerospace/Auto-G2/
# <AUTHOR> <EMAIL>
#
# @board px4_fmu-v2 exclude
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.fw_defaults


param set-default BAT1_CAPACITY 2500
param set-default BAT1_N_CELLS 3

param set-default SENS_BOARD_ROT 8

param set-default FW_AIRSPD_MAX 20
param set-default FW_AIRSPD_MIN 7
param set-default FW_AIRSPD_TRIM 13
param set-default FW_THR_TRIM 0.8

param set-default FW_MAN_P_MAX 25
param set-default FW_MAN_R_MAX 25
param set-default FW_PR_I 0.02
param set-default FW_R_LIM 40
param set-default FW_P_LIM_MAX 25
param set-default FW_P_LIM_MIN -5
param set-default FW_P_RMAX_NEG 20


param set-default CA_AIRFRAME 1

param set-default CA_ROTOR_COUNT 1
param set-default CA_ROTOR0_PX 0.3

param set-default CA_SV_CS_COUNT 3
param set-default CA_SV_CS0_TRQ_R 0
param set-default CA_SV_CS0_TRQ_Y 1
param set-default CA_SV_CS0_TYPE 4
param set-default CA_SV_CS1_TRQ_P 0
param set-default CA_SV_CS1_TRQ_R -0.5
param set-default CA_SV_CS1_TYPE 1
param set-default CA_SV_CS2_TRQ_P 1
param set-default CA_SV_CS2_TYPE 3

param set-default PWM_MAIN_TIM0 50
param set-default PWM_MAIN_TIM1 50
param set-default PWM_MAIN_TIM2 50
