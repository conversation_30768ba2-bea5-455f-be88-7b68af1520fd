#!/bin/sh
#
# @name Vertical Technologies DeltaQuad
#
# @type Standard VTOL
# @class VTOL
#
# @maintainer <PERSON><PERSON> Smeets <<EMAIL>>
#
# @output Motor1 motor 1
# @output Motor2 motor 2
# @output Motor3 motor 3
# @output Motor4 motor 4
# @output Servo1 Right elevon
# @output Servo2 Left elevon
# @output Servo3 Pusher motor
# @output Servo4 Pusher reverse channel
#
# @board px4_fmu-v2 exclude
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.vtol_defaults

param set-default MAV_TYPE 22

param set-default BAT1_CAPACITY 23000
param set-default BAT1_N_CELLS 4
param set-default BAT1_R_INTERNAL 0.0025

param set-default SYS_HAS_NUM_ASPD 0
param set-default CBRK_IO_SAFETY 22027

param set-default EKF2_GPS_POS_X -0.12
param set-default EKF2_IMU_POS_X -0.12

param set-default FW_USE_AIRSPD 0
param set-default NPFG_PERIOD 25
param set-default FW_PR_FF 0.7
param set-default FW_PR_I 0.18
param set-default FW_PR_P 0.15
param set-default FW_P_TC 0.5
param set-default FW_PSP_OFF 5
param set-default FW_R_LIM 35
param set-default FW_RR_FF 0.9
param set-default FW_RR_I 0.08
param set-default FW_RR_P 0.18
param set-default FW_T_CLMB_MAX 3
param set-default FW_T_SINK_MAX 3
param set-default FW_T_SINK_MIN 1
param set-default FW_THR_TRIM 0.70
param set-default FW_THR_SLEW_MAX 1
param set-default FW_P_LIM_MAX 15
param set-default FW_P_LIM_MIN -25
param set-default FW_P_RMAX_NEG 45
param set-default FW_P_RMAX_POS 45
param set-default FW_R_RMAX 50
param set-default FW_THR_MIN 0.55
param set-default FW_BAT_SCALE_EN 1
param set-default FW_T_RLL2THR 20

param set-default MC_ROLLRATE_P 0.16
param set-default MC_ROLLRATE_I 0.01
param set-default MC_PITCHRATE_I 0.05

param set-default MC_YAWRATE_MAX 20
param set-default MC_AIRMODE 1

param set-default MIS_DIST_1WP 100
param set-default MIS_TAKEOFF_ALT 15

param set-default MPC_XY_P 0.8
param set-default MPC_XY_VEL_MAX 5
param set-default MPC_LAND_SPEED 1.2
param set-default MPC_TILTMAX_LND 35
param set-default MPC_Z_VEL_MAX_UP 1.5
param set-default MPC_TKO_RAMP_T 0.8
param set-default MPC_TILTMAX_AIR 25
param set-default MPC_TILTMAX_LND 25
param set-default MPC_YAWRAUTO_MAX 20

param set-default NAV_LOITER_RAD 100

param set-default PWM_MAIN_DIS5 1500
param set-default PWM_MAIN_DIS6 1500
param set-default PWM_MAIN_DIS7 900
param set-default PWM_MAIN_DIS8 900


param set-default SENS_BOARD_ROT 18

# TELEM2 config
param set-default MAV_1_CONFIG 102
param set-default MAV_1_RATE 5000
param set-default MAV_1_FORWARD 1
param set-default SER_TEL2_BAUD 57600

param set-default VT_TYPE 2
param set-default VT_PITCH_MIN 8
param set-default VT_FW_QC_P 55
param set-default VT_FW_QC_R 55
param set-default VT_TRANS_MIN_TM 15
param set-default VT_FWD_THRUST_SC 4
param set-default VT_TRANS_TIMEOUT 22

param set-default COM_RC_OVERRIDE 0

param set-default CA_AIRFRAME 2

param set-default CA_ROTOR_COUNT 5
param set-default CA_ROTOR0_PX 0.1515
param set-default CA_ROTOR0_PY 0.245
param set-default CA_ROTOR0_KM 0.05
param set-default CA_ROTOR1_PX -0.1515
param set-default CA_ROTOR1_PY -0.1875
param set-default CA_ROTOR1_KM 0.05
param set-default CA_ROTOR2_PX 0.1515
param set-default CA_ROTOR2_PY -0.245
param set-default CA_ROTOR2_KM -0.05
param set-default CA_ROTOR3_PX -0.1515
param set-default CA_ROTOR3_PY 0.1875
param set-default CA_ROTOR3_KM -0.05
param set-default CA_ROTOR4_AX 1
param set-default CA_ROTOR4_AZ 0
param set-default CA_ROTOR4_PX 0.2

param set-default CA_SV_CS_COUNT 3
param set-default CA_SV_CS0_TYPE 1
param set-default CA_SV_CS0_TRQ_R -0.5
param set-default CA_SV_CS1_TYPE 2
param set-default CA_SV_CS1_TRQ_R 0.5
param set-default CA_SV_CS2_TYPE 3
param set-default CA_SV_CS2_TRQ_P 1
