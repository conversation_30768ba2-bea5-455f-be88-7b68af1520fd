#!/bin/sh
#
# @name Holybro Kopis 2
#
# @type Quadrotor x
# @class Copter
#
# @maintainer <PERSON> <<EMAIL>>
#
# @board px4_fmu-v2 exclude
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.mc_defaults

param set-default BAT1_N_CELLS 4

param set-default GPS_1_CONFIG 0
param set-default RC_PORT_CONFIG 201

param set-default IMU_GYRO_CUTOFF 80
param set-default IMU_DGYRO_CUTOFF 50
param set-default IMU_GYRO_RATEMAX 2000

param set-default MC_ROLLRATE_P 0.085
param set-default MC_ROLLRATE_I 0.25
param set-default MC_ROLLRATE_D 0.0008
param set-default MC_ROLLRATE_MAX 1600
param set-default MC_ROLL_P 10

param set-default MC_PITCHRATE_P 0.085
param set-default MC_PITCHRATE_I 0.32
param set-default MC_PITCHRATE_D 0.0008
param set-default MC_PITCHRATE_MAX 1600
param set-default MC_PITCH_P 10

param set-default MC_YAWRATE_MAX 1000
param set-default MC_YAWRATE_P 0.15
param set-default MC_YAW_P 4

param set-default MPC_MANTHR_MIN 0
param set-default MPC_MAN_TILT_MAX 60

param set-default OSD_ATXXXX_CFG 1
param set-default TEL_FRSKY_CONFIG 300

param set-default THR_MDL_FAC 0.35

param set-default MPC_THR_CURVE 1
param set-default MPC_THR_HOVER 0.12

param set-default MC_AIRMODE 1

param set-default EV_TSK_RC_LOSS 1

# Square quadrotor X PX4 numbering
param set-default CA_ROTOR_COUNT 4
param set-default CA_ROTOR0_PX 1
param set-default CA_ROTOR0_PY 1
param set-default CA_ROTOR1_PX -1
param set-default CA_ROTOR1_PY -1
param set-default CA_ROTOR2_PX 1
param set-default CA_ROTOR2_PY -1
param set-default CA_ROTOR2_KM -0.05
param set-default CA_ROTOR3_PX -1
param set-default CA_ROTOR3_PY 1
param set-default CA_ROTOR3_KM -0.05

param set-default PWM_MAIN_FUNC1 104
param set-default PWM_MAIN_FUNC2 101
param set-default PWM_MAIN_FUNC3 102
param set-default PWM_MAIN_FUNC4 103

param set-default PWM_MAIN_TIM0 -2
param set-default PWM_MAIN_TIM1 -2
