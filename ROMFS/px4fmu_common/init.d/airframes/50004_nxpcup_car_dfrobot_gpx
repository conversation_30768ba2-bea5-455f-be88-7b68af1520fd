#!/bin/sh
#
# @name NXP Cup car: DF Robot GPX

#
# @type Rover
# @class Rover
#
# @board px4_fmu-v2 exclude
#
# @output Motor1 Speed of left wheels
# @output Servo1 Steering servo
#
# @maintainer <PERSON><PERSON>
#
# @board px4_fmu-v2 exclude
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.rover_defaults

param set-default BAT1_N_CELLS 2

param set-default EKF2_GBIAS_INIT 0.01
param set-default EKF2_ANGERR_INIT 0.01
param set-default EKF2_MAG_TYPE 1

param set-default FW_AIRSPD_MIN 0
param set-default FW_AIRSPD_TRIM 1
param set-default FW_AIRSPD_MAX 3

param set-default GND_THR_CRUISE 0.3
param set-default GND_THR_MAX 0.5

# Differential drive acts like ackermann steering with a maximum turn angle of 60 degrees, or pi/3 radians
param set-default GND_MAX_ANG 1.042
param set-default GND_WHEEL_BASE 0.17

# TODO: Set to -1, to allow reversing. This will require many changes in the codebase
# to support negative throttle.
param set-default GND_THR_MIN 0
param set-default GND_SPEED_P 0.25
param set-default GND_SPEED_I 3
param set-default GND_SPEED_D 0.001
param set-default GND_SPEED_IMAX 0.125
param set-default GND_SPEED_THR_SC 1

param set-default NAV_ACC_RAD 0.5

param set-default CA_AIRFRAME 5

param set-default CA_R_REV 1
param set-default PWM_MAIN_FUNC2 201
param set-default PWM_MAIN_FUNC3 101
param set-default PWM_MAIN_FUNC4 101

# Provide ESC a constant 1500 us pulse to idle
param set-default PWM_MAIN_DIS2 1500
param set-default PWM_MAIN_DIS3 1485
param set-default PWM_MAIN_DIS4 1485
param set-default PWM_MAIN_FAIL3 1485
param set-default PWM_MAIN_FAIL4 1485
param set-default PWM_MAIN_MIN3 970
param set-default PWM_MAIN_MIN4 970
