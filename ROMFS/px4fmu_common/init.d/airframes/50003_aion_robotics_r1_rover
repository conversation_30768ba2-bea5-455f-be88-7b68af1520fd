#!/bin/sh
#
# @name Aion Robotics R1 UGV
#
# @url https://www.aionrobotics.com/r1
#
# @type Rover
# @class Rover
#
# @board px4_fmu-v2 exclude
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.rover_differential_defaults

param set-default BAT1_N_CELLS 4

param set-default EKF2_GBIAS_INIT 0.01
param set-default EKF2_ANGERR_INIT 0.01

# Set geometry & output configration
param set-default RBCLW_ADDRESS 128
param set-default RBCLW_FUNC1 101
param set-default RBCLW_FUNC2 102
param set-default RBCLW_REV 1 # reverse right wheels
