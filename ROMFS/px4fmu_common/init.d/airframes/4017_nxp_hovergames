#!/bin/sh
#
# @name NXP HoverGames
#
# @type Quadrotor x
# @class Copter
#
# @board px4_fmu-v2 exclude
# @board bitcraze_crazyflie exclude
#
# @maintainer <PERSON> <<EMAIL>>
#

. ${R}etc/init.d/rc.mc_defaults

param set-default IMU_DGYRO_CUTOFF 20

param set-default MC_ROLLRATE_P 0.18
param set-default MC_ROLLRATE_I 0.15

param set-default MC_PITCHRATE_P 0.18
param set-default MC_PITCHRATE_I 0.15

# Square quadrotor X PX4 numbering
param set-default CA_ROTOR_COUNT 4
param set-default CA_ROTOR0_PX 1
param set-default CA_ROTOR0_PY 1
param set-default CA_ROTOR1_PX -1
param set-default CA_ROTOR1_PY -1
param set-default CA_ROTOR2_PX 1
param set-default CA_ROTOR2_PY -1
param set-default CA_ROTOR2_KM -0.05
param set-default CA_ROTOR3_PX -1
param set-default CA_ROTOR3_PY 1
param set-default CA_ROTOR3_KM -0.05
