#!/bin/sh
#
# @name Generic Hexarotor x geometry
#
# @type Hexarotor x
# @class Copter
#
# @maintainer <PERSON><PERSON><PERSON> <<EMAIL>>
#
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.mc_defaults

# MAV_TYPE_HEXAROTOR 13
param set-default MAV_TYPE 13

param set-default CA_ROTOR_COUNT 6
param set-default CA_ROTOR0_PX 0
param set-default CA_ROTOR0_PY 0.5
param set-default CA_ROTOR0_KM -0.05
param set-default CA_ROTOR1_PX 0
param set-default CA_ROTOR1_PY -0.5
param set-default CA_ROTOR2_PX 0.43
param set-default CA_ROTOR2_PY -0.25
param set-default CA_ROTOR2_KM -0.05
param set-default CA_ROTOR3_PX -0.43
param set-default CA_ROTOR3_PY 0.25
param set-default CA_ROTOR4_PX 0.43
param set-default CA_ROTOR4_PY 0.25
param set-default CA_ROTOR5_PX -0.43
param set-default CA_ROTOR5_PY -0.25
param set-default CA_ROTOR5_KM -0.05


