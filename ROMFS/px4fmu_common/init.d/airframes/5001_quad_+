#!/bin/sh
#
# @name Generic Quad + geometry
#
# @type Quadrotor +
# @class Copter
#
# @board bitcraze_crazyflie exclude
# @board px4_fmu-v2 exclude
#

. ${R}etc/init.d/rc.mc_defaults

param set-default CA_ROTOR_COUNT 4

param set-default CA_ROTOR0_PX 0
param set-default CA_ROTOR0_PY 1

param set-default CA_ROTOR1_PX 0
param set-default CA_ROTOR1_PY -1

param set-default CA_ROTOR2_PX 1
param set-default CA_ROTOR2_PY 0
param set-default CA_ROTOR2_KM -0.05

param set-default CA_ROTOR3_PX -1
param set-default CA_ROTOR3_PY 0
param set-default CA_ROTOR3_KM -0.05
