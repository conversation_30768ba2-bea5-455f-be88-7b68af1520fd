#!/bin/sh
#
# @name COEX Clover 4
#
# @type Quadrotor x
# @class Copter
#
# @maintainer <PERSON><PERSON> <<EMAIL>>
#
# @board px4_fmu-v2 exclude
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.mc_defaults

param set-default MC_PITCHRATE_P 0.087
param set-default MC_PITCHRATE_I 0.037
param set-default MC_PITCHRATE_D 0.0044
param set-default MC_PITCH_P 8.5
param set-default MC_ROLLRATE_P 0.087
param set-default MC_ROLLRATE_I 0.037
param set-default MC_ROLLRATE_D 0.0044
param set-default MC_ROLL_P 8.5
param set-default MPC_XY_VEL_P_ACC 2.2
param set-default MPC_XY_VEL_D_ACC 0.26
param set-default MPC_XY_P 1.1
param set-default MPC_Z_VEL_P_ACC 4.8
param set-default MPC_Z_P 1.2

# Square quadrotor X PX4 numbering
param set-default CA_ROTOR_COUNT 4
param set-default CA_ROTOR0_PX 1
param set-default CA_ROTOR0_PY 1
param set-default CA_ROTOR1_PX -1
param set-default CA_ROTOR1_PY -1
param set-default CA_ROTOR2_PX 1
param set-default CA_ROTOR2_PY -1
param set-default CA_ROTOR2_KM -0.05
param set-default CA_ROTOR3_PX -1
param set-default CA_ROTOR3_PY 1
param set-default CA_ROTOR3_KM -0.05

param set-default PWM_MAIN_FUNC1 101
param set-default PWM_MAIN_FUNC2 102
param set-default PWM_MAIN_FUNC3 103
param set-default PWM_MAIN_FUNC4 104
