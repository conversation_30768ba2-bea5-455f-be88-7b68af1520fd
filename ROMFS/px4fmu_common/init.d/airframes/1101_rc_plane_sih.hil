#!/bin/sh
#
# @name SIH plane AERT
#
# @type Simulation
# @class Plane
#
# @maintainer <PERSON><PERSON> <<EMAIL>>
#
# @board px4_fmu-v2 exclude
#

. ${R}etc/init.d/rc.fw_defaults


param set UAVCAN_ENABLE 0

param set-default CA_AIRFRAME 1
param set-default CA_ROTOR_COUNT 1
param set-default CA_ROTOR0_PX 0.3
param set-default CA_SV_CS_COUNT 4
param set-default CA_SV_CS0_TRQ_R 0.5
param set-default CA_SV_CS0_TYPE 2
param set-default CA_SV_CS1_TRQ_P 1
param set-default CA_SV_CS1_TYPE 3
param set-default CA_SV_CS2_TRQ_Y 1
param set-default CA_SV_CS2_TYPE 4
param set-default CA_SV_CS3_TYPE 10

param set-default HIL_ACT_REV 2
param set-default HIL_ACT_FUNC1 201
param set-default HIL_ACT_FUNC2 202
param set-default HIL_ACT_FUNC3 203
param set-default HIL_ACT_FUNC4 101
param set-default HIL_ACT_FUNC5 204
param set-default HIL_ACT_FUNC6 400

# set SYS_HITL to 2 to start the SIH and avoid sensors startup
param set-default SYS_HITL 2

# disable some checks to allow to fly:
# - without real battery
param set-default CBRK_SUPPLY_CHK 894281
# - without safety switch
param set-default CBRK_IO_SAFETY 22027

param set-default BAT_N_CELLS 3

param set SIH_T_MAX 6
param set SIH_MASS 0.3
param set SIH_IXX 0.00402
param set SIH_IYY 0.0144
param set SIH_IZZ 0.0177
param set SIH_IXZ 0.00046
param set SIH_KDV 0.2

param set SIH_VEHICLE_TYPE 1 	# sih as fixed wing
param set RWTO_TKOFF 1  # enable takeoff from runway (as opposed to launched)
