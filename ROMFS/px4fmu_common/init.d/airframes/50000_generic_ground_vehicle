#!/bin/sh
#
# @name Generic Ground Vehicle (Ackermann)
#
# @type Rover
# @class Rover
#
# @output Motor1 throttle
# @output Servo1 steering
#
# @maintainer
#
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.rover_defaults


param set-default BAT1_N_CELLS 2

param set-default EKF2_ANGERR_INIT 0.01
param set-default EKF2_GBIAS_INIT 0.01
param set-default EKF2_MAG_TYPE 1

param set-default FW_AIRSPD_MAX 3
param set-default FW_AIRSPD_MIN 0
param set-default FW_AIRSPD_TRIM 1

# Settings for a typical wheelbase 0f 0.3m
param set-default GND_L1_DIST 1
param set-default GND_L1_PERIOD 5
param set-default GND_SP_CTRL_MODE 1
param set-default GND_SPEED_P 0.25
param set-default GND_SPEED_I 3
param set-default GND_SPEED_D 0.001
param set-default GND_SPEED_IMAX 0.125
param set-default GND_SPEED_THR_SC 1
param set-default GND_THR_CRUISE 0.3
param set-default GND_THR_MAX 0.5
param set-default GND_THR_MIN 0

param set-default NAV_ACC_RAD 0.5

param set-default CA_AIRFRAME 5

param set-default CA_R_REV 1
param set-default PWM_MAIN_FUNC1 201
param set-default PWM_MAIN_FUNC2 201
param set-default PWM_MAIN_FUNC6 101
param set-default PWM_MAIN_FUNC7 101


