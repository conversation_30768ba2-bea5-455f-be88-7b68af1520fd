#!/bin/sh
#
# @name SIH Quadcopter X
#
# @type Simulation
# @class Copter
#
# @maintainer <PERSON><PERSON> <<EMAIL>>
#
# @board px4_fmu-v2 exclude
#

. ${R}etc/init.d/rc.mc_defaults

param set UAVCAN_ENABLE 0

# set SYS_HITL to 2 to start the SIH and avoid sensors startup
param set SYS_HITL 2

# disable some checks to allow to fly:
# - without real battery
param set-default CBRK_SUPPLY_CHK 894281
# - without safety switch
param set-default CBRK_IO_SAFETY 22027

param set SIH_VEHICLE_TYPE 0

# Square quadrotor X PX4 numbering
param set-default CA_ROTOR_COUNT 4
param set-default CA_ROTOR0_PX 1
param set-default CA_ROTOR0_PY 1
param set-default CA_ROTOR1_PX -1
param set-default CA_ROTOR1_PY -1
param set-default CA_ROTOR2_PX 1
param set-default CA_ROTOR2_PY -1
param set-default CA_ROTOR2_KM -0.05
param set-default CA_ROTOR3_PX -1
param set-default CA_ROTOR3_PY 1
param set-default CA_ROTOR3_KM -0.05

param set-default HIL_ACT_FUNC1 101
param set-default HIL_ACT_FUNC2 102
param set-default HIL_ACT_FUNC3 103
param set-default HIL_ACT_FUNC4 104
