#!/bin/sh
#
# @name BetaFPV Beta75X 2S Brushless Whoop
#
# @type Quadrotor H
# @class Copter
#
# @maintainer <PERSON> <<EMAIL>>
#
# @board px4_fmu-v2 exclude
# @board px4_fmu-v3 exclude
# @board px4_fmu-v4 exclude
# @board px4_fmu-v4pro exclude
# @board px4_fmu-v5 exclude
# @board px4_fmu-v5x exclude
# @board bitcraze_crazyflie exclude
#

. ${R}etc/init.d/rc.mc_defaults

param set-default CBRK_SUPPLY_CHK 894281

param set-default IMU_GYRO_CUTOFF 100
param set-default IMU_DGYRO_CUTOFF 60

param set-default MC_AIRMODE 2
param set-default MC_PITCHRATE_D 0.001
param set-default MC_PITCHRATE_I 0.5
param set-default MC_PITCHRATE_MAX 600
param set-default MC_PITCHRATE_P 0.075
param set-default MC_PITCH_P 6
param set-default MC_ROLLRATE_D 0.001
param set-default MC_ROLLRATE_I 0.4
param set-default MC_ROLLRATE_MAX 600
param set-default MC_ROLLRATE_P 0.075
param set-default MC_YAWRATE_I 0.3
param set-default MC_YAWRATE_MAX 400
param set-default MC_YAWRATE_P 0.12
param set-default MC_YAW_P 4

param set-default MPC_MANTHR_MIN 0
param set-default MPC_MAN_TILT_MAX 60

param set-default SYS_HAS_BARO 0
param set-default SYS_HAS_MAG 0

param set-default BAT1_N_CELLS 2

# Square quadrotor X with reverse turn direction
param set-default CA_ROTOR_COUNT 4
param set-default CA_ROTOR0_PX 1
param set-default CA_ROTOR0_PY 1
param set-default CA_ROTOR0_KM -0.05
param set-default CA_ROTOR1_PX -1
param set-default CA_ROTOR1_PY -1
param set-default CA_ROTOR1_KM -0.05
param set-default CA_ROTOR2_PX 1
param set-default CA_ROTOR2_PY -1
param set-default CA_ROTOR3_PX -1
param set-default CA_ROTOR3_PY 1

param set-default PWM_MAIN_FUNC1 104
param set-default PWM_MAIN_FUNC2 101
param set-default PWM_MAIN_FUNC3 102
param set-default PWM_MAIN_FUNC4 103
