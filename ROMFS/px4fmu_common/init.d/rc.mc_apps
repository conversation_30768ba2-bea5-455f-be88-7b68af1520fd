#!/bin/sh
#
# Standard apps for multirotors. Attitude/Position estimator, Attitude/Position control.
#
# NOTE: Script variables are declared/initialized/unset in the rcS script.
#

#
# Start Control Allocator
#
control_allocator start

#
# Start Multicopter Rate Controller.
#
mc_rate_control start

#
# Start Multicopter Attitude Controller.
#
mc_att_control start

if param greater -s MC_AT_EN 0
then
	mc_autotune_attitude_control start
fi

#
# Start Multicopter Position Controller.
#
mc_hover_thrust_estimator start
flight_mode_manager start
mc_pos_control start

#
# Start Multicopter Land Detector.
#
land_detector start multicopter
