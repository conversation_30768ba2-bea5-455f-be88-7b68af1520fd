#!/bin/sh
#
# Thermal Calibration startup script.
#
# NOTE: Script variables are declared/initialized/unset in the rcS script.
#

set TEMP_COMP_START ""

if param compare -s TC_A_ENABLE 1
then
	set TEMP_COMP_START "true"
fi

if param compare -s TC_B_ENABLE 1
then
	set TEMP_COMP_START "true"
fi

if param compare -s TC_G_ENABLE 1
then
	set TEMP_COMP_START "true"
fi

if param compare -s TC_M_ENABLE 1
then
	set TEMP_COMP_START "true"
fi

if [ "x$TEMP_COMP_START" != "x" ]
then
	temperature_compensation start
fi

unset TEMP_COMP_START


#
# Determine if a thermal calibration should be started.
#

set TEMP_CALIB_ARGS ""

if param compare SYS_CAL_ACCEL 1
then
	set TEMP_CALIB_ARGS "${TEMP_CALIB_ARGS} -a"
	param set SYS_CAL_ACCEL 0
fi

if param compare SYS_CAL_GYRO 1
then
	set TEMP_CALIB_ARGS "${TEMP_CALIB_ARGS} -g"
	param set SYS_CAL_GYRO 0
fi

if param compare SYS_CAL_BARO 1
then
	set TEMP_CALIB_ARGS "${TEMP_CALIB_ARGS} -b"
	param set SYS_CAL_BARO 0
fi

#
# Start the thermal calibration cycle.
#
if [ "x$TEMP_CALIB_ARGS" != "x" ]
then
	temperature_compensation calibrate ${TEMP_CALIB_ARGS}
fi

unset TEMP_CALIB_ARGS
