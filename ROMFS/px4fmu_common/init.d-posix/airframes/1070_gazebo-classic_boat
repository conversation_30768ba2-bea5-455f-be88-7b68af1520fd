#!/bin/sh
#
# @name Boat
#

. ${R}etc/init.d/rc.boat_defaults

param set-default GND_L1_DIST 5
param set-default GND_L1_PERIOD 100
param set-default GND_SP_CTRL_MODE 1
param set-default GND_SPEED_D 0.001
param set-default GND_SPEED_I 8
param set-default GND_SPEED_IMAX 0.125
param set-default GND_SPEED_P 2
param set-default GND_SPEED_THR_SC 1
param set-default GND_SPEED_TRIM 1
param set-default GND_THR_CRUISE 0.85
param set-default GND_THR_MAX 1
param set-default GND_THR_MIN 0

param set-default NAV_ACC_RAD 0.5
param set-default NAV_LOITER_RAD 2

param set-default GND_MAX_ANG 0.6
param set-default GND_WHEEL_BASE 2

param set-default CA_AIRFRAME 9

param set-default CA_ROTOR_COUNT 2
param set-default CA_ROTOR0_AX 1
param set-default CA_ROTOR0_AZ 0
param set-default CA_ROTOR0_KM 0
param set-default CA_ROTOR0_PX -2
param set-default CA_ROTOR0_PY -1
param set-default CA_ROTOR1_AX 1
param set-default CA_ROTOR1_AZ 0
param set-default CA_ROTOR1_KM 0
param set-default CA_ROTOR1_PX -2
param set-default CA_ROTOR1_PY 1
param set-default CA_R_REV 3

param set-default PWM_MAIN_FUNC1 101
param set-default PWM_MAIN_FUNC2 102

