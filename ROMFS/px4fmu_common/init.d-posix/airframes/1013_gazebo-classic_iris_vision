#!/bin/sh
#
# @name 3DR Iris Quadrotor SITL (Vision)
#
# @type Quadrotor Wide
#

. ${R}etc/init.d-posix/airframes/10016_gazebo-classic_iris

# EKF2: Vision position and heading, no GPS
param set-default EKF2_EV_DELAY 5
param set-default EKF2_EV_CTRL 15
param set-default EKF2_HGT_REF 3
param set-default EKF2_GPS_CTRL 0

# LPE: Vision + baro
param set-default LPE_FUSION 132

# AEQ: External heading set to use vision input
param set-default ATT_EXT_HDG_M 1

