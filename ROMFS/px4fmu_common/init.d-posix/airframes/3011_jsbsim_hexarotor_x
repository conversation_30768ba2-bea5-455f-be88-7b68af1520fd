#!/bin/sh
#
# @name Hexacopter SITL model for JSBSim
#
# @type Hexarotor x
#
# @maintainer <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
#

. ${R}etc/init.d/rc.mc_defaults

param set-default MAV_TYPE 13

param set-default MC_PITCHRATE_P 0.1
param set-default MC_PITCHRATE_I 0.05
param set-default MC_PITCH_P 6
param set-default MC_ROLLRATE_I 0.1
param set-default MC_ROLL_P 6
param set-default MPC_XY_VEL_I_ACC 4
param set-default MPC_XY_VEL_P_ACC 3

param set-default RTL_DESCEND_ALT 10

param set-default TRIG_INTERFACE 3
param set-default TRIG_MODE 4
param set-default MNT_MODE_IN 4
param set-default MNT_DO_STAB 2


param set-default CA_ROTOR0_PX 0
param set-default CA_ROTOR0_PY 1
param set-default CA_ROTOR0_KM -0.05
param set-default CA_ROTOR1_PX 0
param set-default CA_ROTOR1_PY -1
param set-default CA_ROTOR1_KM 0.05
param set-default CA_ROTOR2_PX 0.866025
param set-default CA_ROTOR2_PY -0.5
param set-default CA_ROTOR2_KM -0.05
param set-default CA_ROTOR3_PX -0.866025
param set-default CA_ROTOR3_PY 0.5
param set-default CA_ROTOR3_KM 0.05
param set-default CA_ROTOR4_PX 0.866025
param set-default CA_ROTOR4_PY 0.5
param set-default CA_ROTOR4_KM 0.05
param set-default CA_ROTOR5_PX -0.866025
param set-default CA_ROTOR5_PY -0.5
param set-default CA_ROTOR5_KM -0.05

param set-default PWM_MAIN_FUNC1 101
param set-default PWM_MAIN_FUNC2 102
param set-default PWM_MAIN_FUNC3 103
param set-default PWM_MAIN_FUNC4 104
param set-default PWM_MAIN_FUNC5 105
param set-default PWM_MAIN_FUNC6 106
