#!/bin/sh
#
# @name ThunderFly TF-G2
# ThunderFly TF-G2 autogyro airframe. Only for FlightGear simulator
#
# @type Autogyro
# @class Autogyro
#
# @url https://github.com/ThunderFly-aerospace/TF-G2/
#
#

. ${R}etc/init.d/rc.fw_defaults

param set-default FW_AIRSPD_STALL 5

param set-default FW_P_RMAX_NEG 20
param set-default FW_W_RMAX 10
param set-default FW_W_EN 1

param set-default FW_RR_P 0.08

param set-default MIS_TAKEOFF_ALT 7

param set-default NAV_ACC_RAD 20
param set-default NAV_DLL_ACT 2
param set-default NAV_LOITER_RAD 50

# Parameters related to autogyro takeoff PR
#param set-default AG_TKOFF 1
#param set-default AG_PROT_TYPE 1
#param set-default AG_PROT_MIN_RPM 50
#param set-default AG_PROT_TRG_RPM 900
#param set-defoult AG_ROTOR_RPM 900

param set-default FW_ARSP_SCALE_EN 0

param set-default FW_AIRSPD_MAX 35
param set-default FW_AIRSPD_MIN 7

param set-default FW_P_LIM_MAX 25
param set-default FW_P_LIM_MIN -5
param set-default FW_R_LIM 30

param set-default FW_MAN_R_MAX 30

param set-default FW_THR_CRUISE 0.8
param set-default FW_THR_IDLE 0
param set-default COM_DISARM_PRFLT 0

param set-default CA_AIRFRAME 1

param set-default CA_ROTOR_COUNT 1
param set-default CA_ROTOR0_PX 0.3

param set-default CA_SV_CS_COUNT 3
param set-default CA_SV_CS0_TRQ_R 0
param set-default CA_SV_CS0_TRQ_Y 1
param set-default CA_SV_CS0_TYPE 4
param set-default CA_SV_CS1_TRQ_P 0
param set-default CA_SV_CS1_TRQ_R -0.5
param set-default CA_SV_CS1_TYPE 1
param set-default CA_SV_CS2_TRQ_P 1
param set-default CA_SV_CS2_TYPE 3
param set-default PWM_MAIN_FUNC1 201
param set-default PWM_MAIN_FUNC2 101
param set-default PWM_MAIN_FUNC3 202
param set-default PWM_MAIN_FUNC4 203
param set-default PWM_MAIN_FUNC5 407
param set-default PWM_MAIN_FUNC6 408
param set-default PWM_MAIN_FUNC7 409

