#!/bin/sh
#
# @name Plane SITL
#

. ${R}etc/init.d/rc.fw_defaults

param set-default FW_LND_ANG 5
param set-default FW_LND_FL_PMIN 9.5
param set-default FW_LND_FL_PMAX 20
param set-default FW_LND_FLALT 5

param set-default NPFG_PERIOD 25

param set-default FW_PR_FF 0.40
param set-default FW_PR_I 0.05
param set-default FW_PR_P 0.05

param set-default FW_R_TC 0.45
param set-default FW_RR_FF 0.40
param set-default FW_RR_I 0.132
param set-default FW_RR_P 0.085

param set-default FW_W_EN 1

param set-default MIS_TAKEOFF_ALT 20
param set-default MIS_DIST_1WP 2500

param set-default NAV_ACC_RAD 15
param set-default NAV_DLL_ACT 2

param set-default RWTO_TKOFF 1
param set-default RWTO_PSP 8

param set-default CA_AIRFRAME 1

param set-default CA_ROTOR_COUNT 1
param set-default CA_ROTOR0_PX 0.3

param set-default CA_SV_CS_COUNT 6
param set-default CA_SV_CS0_TRQ_R -0.5
param set-default CA_SV_CS0_TYPE 1
param set-default CA_SV_CS1_TRQ_R 0.5
param set-default CA_SV_CS1_TYPE 2
param set-default CA_SV_CS2_TRQ_P 1
param set-default CA_SV_CS2_TYPE 3
param set-default CA_SV_CS3_TRQ_Y 1
param set-default CA_SV_CS3_TYPE 4
param set-default CA_SV_CS4_TYPE 9
param set-default CA_SV_CS5_TYPE 10
param set-default PWM_MAIN_FUNC3 204
param set-default PWM_MAIN_FUNC4 205
param set-default PWM_MAIN_FUNC5 101
param set-default PWM_MAIN_FUNC6 201
param set-default PWM_MAIN_FUNC7 202
param set-default PWM_MAIN_FUNC8 203
param set-default PWM_MAIN_FUNC9 206
param set-default PWM_MAIN_REV 256
