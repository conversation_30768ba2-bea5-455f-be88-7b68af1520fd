#!/bin/sh
#
# @name ThunderFly TF-R1 UAV Rover
# @type Rover
# @class Rover
#
# @url https://github.com/ThunderFly-aerospace/TF-R1/
# @maintainer ThunderFly s.r.o.
#

. ${R}etc/init.d/rc.rover_defaults

param set-default MAV_TYPE 10

param set-default GND_L1_DIST 5
param set-default GND_SP_CTRL_MODE 1
param set-default GND_SPEED_D 3
param set-default GND_SPEED_I 0.001
param set-default GND_SPEED_IMAX 0.125
param set-default GND_SPEED_P 0.25
param set-default GND_SPEED_THR_SC 1
param set-default GND_SPEED_TRIM 15
param set-default GND_SPEED_MAX 25
param set-default GND_THR_CRUISE 0.3
param set-default GND_THR_MAX 0.5
param set-default GND_THR_MIN 0

param set-default NAV_ACC_RAD 0.5
param set-default NAV_LOITER_RAD 2

param set-default GND_MAX_ANG 0.6
param set-default GND_WHEEL_BASE 3

param set-default CA_AIRFRAME 5

param set-default CA_R_REV 1
param set-default PWM_MAIN_FUNC1 201
param set-default PWM_MAIN_FUNC2 201
param set-default PWM_MAIN_FUNC6 101
param set-default PWM_MAIN_FUNC7 101

