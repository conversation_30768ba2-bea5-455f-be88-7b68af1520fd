#!/bin/sh
#
# @name 6DoF Omnicopter SITL
#
# @type Quadrotor Wide
#
# @maintainer <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
#

. ${R}etc/init.d/rc.mc_defaults


param set-default CA_AIRFRAME 0

param set-default CA_ROTOR_COUNT 8
param set-default CA_R_REV 255

param set-default CA_ROTOR0_PX 0.14435
param set-default CA_ROTOR0_PY -0.14435
param set-default CA_ROTOR0_PZ -0.14435
param set-default CA_ROTOR0_KM 0.05 # CCW
param set-default CA_ROTOR0_AX -0.788675
param set-default CA_ROTOR0_AY -0.211325
param set-default CA_ROTOR0_AZ -0.57735

param set-default CA_ROTOR1_PX -0.14435
param set-default CA_ROTOR1_PY -0.14435
param set-default CA_ROTOR1_PZ -0.14435
param set-default CA_ROTOR1_KM 0.05
param set-default CA_ROTOR1_AX 0.211325
param set-default CA_ROTOR1_AY -0.788675
param set-default CA_ROTOR1_AZ 0.57735

param set-default CA_ROTOR2_PX 0.14435
param set-default CA_ROTOR2_PY 0.14435
param set-default CA_ROTOR2_PZ -0.14435
param set-default CA_ROTOR2_KM 0.05
param set-default CA_ROTOR2_AX -0.211325
param set-default CA_ROTOR2_AY 0.788675
param set-default CA_ROTOR2_AZ 0.57735

param set-default CA_ROTOR3_PX -0.14435
param set-default CA_ROTOR3_PY 0.14435
param set-default CA_ROTOR3_PZ -0.14435
param set-default CA_ROTOR3_KM 0.05
param set-default CA_ROTOR3_AX 0.788675
param set-default CA_ROTOR3_AY 0.211325
param set-default CA_ROTOR3_AZ -0.57735

param set-default CA_ROTOR4_PX 0.14435
param set-default CA_ROTOR4_PY -0.14435
param set-default CA_ROTOR4_PZ 0.14435
param set-default CA_ROTOR4_KM 0.05
param set-default CA_ROTOR4_AX 0.788675
param set-default CA_ROTOR4_AY 0.211325
param set-default CA_ROTOR4_AZ -0.57735

param set-default CA_ROTOR5_PX -0.14435
param set-default CA_ROTOR5_PY -0.14435
param set-default CA_ROTOR5_PZ 0.14435
param set-default CA_ROTOR5_KM 0.05
param set-default CA_ROTOR5_AX -0.211325
param set-default CA_ROTOR5_AY 0.788675
param set-default CA_ROTOR5_AZ 0.57735

param set-default CA_ROTOR6_PX 0.14435
param set-default CA_ROTOR6_PY 0.14435
param set-default CA_ROTOR6_PZ 0.14435
param set-default CA_ROTOR6_KM 0.05
param set-default CA_ROTOR6_AX 0.211325
param set-default CA_ROTOR6_AY -0.788675
param set-default CA_ROTOR6_AZ 0.57735

param set-default CA_ROTOR7_PX -0.14435
param set-default CA_ROTOR7_PY 0.14435
param set-default CA_ROTOR7_PZ 0.14435
param set-default CA_ROTOR7_KM 0.05
param set-default CA_ROTOR7_AX -0.788675
param set-default CA_ROTOR7_AY -0.211325
param set-default CA_ROTOR7_AZ -0.57735

param set-default PWM_MAIN_FUNC1 101
param set-default PWM_MAIN_FUNC2 102
param set-default PWM_MAIN_FUNC3 103
param set-default PWM_MAIN_FUNC4 104
param set-default PWM_MAIN_FUNC5 105
param set-default PWM_MAIN_FUNC6 106
param set-default PWM_MAIN_FUNC7 107
param set-default PWM_MAIN_FUNC8 108

# disable MC desaturation which improves attitude tracking
param set-default CA_METHOD 0

# disable attitude failure detection
param set-default FD_FAIL_P 0
param set-default FD_FAIL_R 0

