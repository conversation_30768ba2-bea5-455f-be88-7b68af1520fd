#!/bin/sh
#
# @name Cloudship
# @type Airship
# @class Airship
#
# @output MAIN1 thrust tilt
# @output MAIN2 starboard thruster
# @output MAIN3 port thruster
# @output MAIN4 tail thruster

. ${R}etc/init.d/rc.airship_defaults

param set-default CA_AIRFRAME 9

param set-default CA_ROTOR_COUNT 3
param set-default CA_ROTOR0_AX 1
param set-default CA_ROTOR0_AZ 0
param set-default CA_ROTOR0_KM 0
param set-default CA_ROTOR0_PY 2
param set-default CA_ROTOR1_AX 1
param set-default CA_ROTOR1_AZ 0
param set-default CA_ROTOR1_KM 0
param set-default CA_ROTOR1_PY -2
param set-default CA_ROTOR2_AY -1
param set-default CA_ROTOR2_AZ 0
param set-default CA_ROTOR2_KM 0
param set-default CA_ROTOR2_PX -10

param set-default CA_SV_CS_COUNT 1
param set-default CA_SV_CS0_TRQ_P 1

param set-default CA_R_REV 7

param set-default PWM_MAIN_FUNC1 101
param set-default PWM_MAIN_FUNC2 102
param set-default PWM_MAIN_FUNC3 201
param set-default PWM_MAIN_FUNC4 103

