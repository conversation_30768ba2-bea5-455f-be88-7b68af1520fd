#!/bin/sh

# EKF2 replay script

# shellcheck disable=SC2154
if [ ! -f ${replay} ]; then
	echo "Invalid replay log file ${replay}"
	exit 1
fi

if [ ! -f replay_params.txt ]; then
	echo "Creating $(pwd)/replay_params.txt"
	ulog_params -i "${replay}" -l ' ' | grep -e '^EKF2' > replay_params.txt
fi

publisher_rules_file="orb_publisher.rules"
cat <<EOF > "$publisher_rules_file"
restrict_topics: sensor_combined, vehicle_gps_position, vehicle_land_detected
module: replay
ignore_others: false
EOF

param set SDLOG_DIRS_MAX 7
param set SDLOG_PROFILE 3

# apply all params before ekf starts, as some params cannot be changed after startup
replay tryapplyparams
ekf2 start -r
logger start -f -t -b 1000 -p vehicle_attitude
replay start
