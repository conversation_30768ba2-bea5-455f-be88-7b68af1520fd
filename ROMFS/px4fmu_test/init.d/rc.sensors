#!/bin/sh
#
# Standard startup script for onboard sensor drivers.
#

if ms5611 -s start
then
fi

# Blacksheep telemetry
if bst start -X
then
fi

if board_adc start
then
fi

if sdp3x start -X
then
fi

if ms5525dso start -X
then
fi

if ms4525do start -X
then
fi

if ets_airspeed start -X
then
fi

if lightware_laser_i2c start
then
fi

# Wait 20 ms for sensors (because we need to wait for the HRT and work queue callbacks to fire)
usleep 20000
if sensors start
then
fi
